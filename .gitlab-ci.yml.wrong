#image: gradle:8.12-jdk21
image: docker:dind

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG

stages:
  - build
  - test
  - docker

build:
  stage: build
  image: eclipse-temurin:21
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - build/libs/*.jar
    expire_in: 1 week

test:
  stage: test
  image: eclipse-temurin:21
  script:
    - ./gradlew test

docker:
  stage: docker
  variables:
#    DOCKER_HOST: unix:///var/run/docker.sock
#    DOCKER_HOST: tcp://docker:2375
    DOCKER_HOST: tcp://localhost:2375
#  image: docker
#  services:
#    - docker:dind
#  before_script:
#    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
#    - env | sort
    - hostname
    - which docker
    - docker ps
    - docker build --pull -t $CONTAINER_IMAGE .
    - docker push $CONTAINER_IMAGE
#  rules:
#    - if: $CI_COMMIT_BRANCH == "main"
  only:
    - branches
    - tags