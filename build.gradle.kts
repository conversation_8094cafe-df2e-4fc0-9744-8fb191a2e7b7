plugins {
	java
	id("org.springframework.boot") version "3.5.0"
	id("io.spring.dependency-management") version "1.1.7"
	id("org.openapi.generator") version "7.10.0"
	jacoco
}

group = "com.mymueller"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	runtimeOnly("org.postgresql:postgresql")

	// OpenAPI dependencies
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0")
	implementation("org.openapitools:jackson-databind-nullable:0.2.6")

	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
	testRuntimeOnly("com.h2database:h2")

	developmentOnly("org.springframework.boot:spring-boot-devtools")
}

tasks.withType<Test> {
	useJUnitPlatform()
}

// Configure OpenAPI code generation
openApiGenerate {
	generatorName.set("spring")
	inputSpec.set("$rootDir/src/main/resources/api/transaction-api.yaml")
	outputDir.set("$buildDir/generated")
	apiPackage.set("com.mymueller.transactionsstore.api")
	modelPackage.set("com.mymueller.transactionsstore.model")
	configOptions.set(mapOf(
		"dateLibrary" to "java8",
		"interfaceOnly" to "true",
		"skipDefaultInterface" to "true",
		"useTags" to "true",
		"useSpringBoot3" to "true",
		"openApiNullable" to "false",
		"serializationLibrary" to "jackson"
	))
}

// Add generated sources to compilation
sourceSets {
	main {
		java {
			srcDir("$buildDir/generated/src/main/java")
		}
	}
}

// Ensure code generation runs before compilation
tasks.compileJava {
	dependsOn(tasks.openApiGenerate)
}
