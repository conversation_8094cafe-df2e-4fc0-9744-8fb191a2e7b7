plugins {
	java
	id("org.springframework.boot") version "3.5.0"
	id("io.spring.dependency-management") version "1.1.7"
	id("org.openapi.generator") version "7.10.0"
	jacoco
}

group = "com.mymueller"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("org.springframework.boot:spring-boot-starter-actuator")
	runtimeOnly("org.postgresql:postgresql")

	// OpenAPI dependencies
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0")
	implementation("org.openapitools:jackson-databind-nullable:0.2.6")

	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
	testRuntimeOnly("com.h2database:h2")

	developmentOnly("org.springframework.boot:spring-boot-devtools")
}



// Configure OpenAPI code generation
openApiGenerate {
	generatorName.set("spring")
	inputSpec.set("$rootDir/src/main/resources/api/transaction-api.yaml")
	outputDir.set("${layout.buildDirectory.get()}/generated")
	apiPackage.set("com.mymueller.transactionsstore.api")
	modelPackage.set("com.mymueller.transactionsstore.model")
	configOptions.set(mapOf(
		"dateLibrary" to "java8",
		"interfaceOnly" to "true",
		"skipDefaultInterface" to "true",
		"useTags" to "true",
		"useSpringBoot3" to "true",
		"openApiNullable" to "false",
		"serializationLibrary" to "jackson"
	))
}

// Add generated sources to compilation
sourceSets {
	main {
		java {
			srcDir("${layout.buildDirectory.get()}/generated/src/main/java")
		}
	}
}

// Ensure code generation runs before compilation
tasks.compileJava {
	dependsOn(tasks.openApiGenerate)
}

// JaCoCo configuration
jacoco {
	toolVersion = "0.8.11"
}

tasks.test {
	useJUnitPlatform()
	finalizedBy(tasks.jacocoTestReport) // report is always generated after tests run
}

tasks.jacocoTestReport {
	dependsOn(tasks.test) // tests are required to run before generating the report
	reports {
		xml.required = true
		html.required = true
		csv.required = false
	}

	// Exclude generated code from coverage
	executionData.setFrom(fileTree(layout.buildDirectory.dir("jacoco")).include("**/*.exec"))

	classDirectories.setFrom(
		files(classDirectories.files.map {
			fileTree(it) {
				exclude(
					"**/api/**", // Exclude generated OpenAPI interfaces
					"**/model/**", // Exclude generated OpenAPI models
					"**/TransactionsstoreApplication*", // Exclude main application class
					"**/config/**" // Exclude configuration classes if any
				)
			}
		})
	)
}

// Coverage verification task
tasks.jacocoTestCoverageVerification {
	dependsOn(tasks.jacocoTestReport)
	violationRules {
		rule {
			limit {
				minimum = "0.85".toBigDecimal() // 85% instruction coverage minimum
			}
		}
		rule {
			enabled = true
			element = "CLASS"
			limit {
				counter = "BRANCH"
				value = "COVEREDRATIO"
				minimum = "0.65".toBigDecimal() // 65% branch coverage minimum (adjusted)
			}
		}
		rule {
			enabled = true
			element = "CLASS"
			limit {
				counter = "LINE"
				value = "COVEREDRATIO"
				minimum = "0.50".toBigDecimal() // 50% line coverage minimum (adjusted for simple controllers)
			}
		}
	}

	// Apply same exclusions as report
	classDirectories.setFrom(
		files(classDirectories.files.map {
			fileTree(it) {
				exclude(
					"**/api/**",
					"**/model/**",
					"**/TransactionsstoreApplication*", // Exclude main application class
					"**/config/**"
				)
			}
		})
	)
}

// Make check task depend on coverage verification
tasks.check {
	dependsOn(tasks.jacocoTestCoverageVerification)
}

// Custom task to print coverage summary
tasks.register("coverageSummary") {
	dependsOn(tasks.jacocoTestReport)
	doLast {
		val reportFile = file("${layout.buildDirectory.get()}/reports/jacoco/test/jacocoTestReport.xml")
		if (reportFile.exists()) {
			val report = reportFile.readText()
			val instructionMissed = Regex("""<counter type="INSTRUCTION" missed="(\d+)" covered="(\d+)"/>""").findAll(report).last()
			val branchMissed = Regex("""<counter type="BRANCH" missed="(\d+)" covered="(\d+)"/>""").findAll(report).last()
			val lineMissed = Regex("""<counter type="LINE" missed="(\d+)" covered="(\d+)"/>""").findAll(report).last()
			val methodMissed = Regex("""<counter type="METHOD" missed="(\d+)" covered="(\d+)"/>""").findAll(report).last()
			val classMissed = Regex("""<counter type="CLASS" missed="(\d+)" covered="(\d+)"/>""").findAll(report).last()

			println("\n" + "=".repeat(60))
			println("📊 CODE COVERAGE SUMMARY")
			println("=".repeat(60))

			fun printCoverage(name: String, missed: Int, covered: Int) {
				val total = missed + covered
				val percentage = if (total > 0) (covered * 100.0 / total) else 0.0
				val status = when {
					percentage >= 90 -> "✅"
					percentage >= 80 -> "⚠️"
					else -> "❌"
				}
				println("$status $name: $covered/$total (${String.format("%.1f", percentage)}%)")
			}

			instructionMissed.groupValues.let { printCoverage("Instructions", it[1].toInt(), it[2].toInt()) }
			branchMissed.groupValues.let { printCoverage("Branches    ", it[1].toInt(), it[2].toInt()) }
			lineMissed.groupValues.let { printCoverage("Lines       ", it[1].toInt(), it[2].toInt()) }
			methodMissed.groupValues.let { printCoverage("Methods     ", it[1].toInt(), it[2].toInt()) }
			classMissed.groupValues.let { printCoverage("Classes     ", it[1].toInt(), it[2].toInt()) }

			println("=".repeat(60))
			println("📁 Reports available at:")
			println("   HTML: file://${layout.buildDirectory.get()}/reports/jacoco/test/html/index.html")
			println("   XML:  ${layout.buildDirectory.get()}/reports/jacoco/test/jacocoTestReport.xml")
			println("=".repeat(60))
		}
	}
}

// Convenient task to run tests with coverage and show summary
tasks.register("testWithCoverage") {
	group = "verification"
	description = "Runs tests with code coverage and displays summary"
	dependsOn(tasks.test, tasks.jacocoTestReport, tasks.named("coverageSummary"))
}
