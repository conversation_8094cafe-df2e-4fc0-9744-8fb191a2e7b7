# GitLab Badges Example

You can add these badges to your README.md to show build status, test results, and coverage:

## Pipeline Status Badge
```markdown
[![pipeline status](https://gitlab.com/your-namespace/plusparser/badges/main/pipeline.svg)](https://gitlab.com/your-namespace/plusparser/-/commits/main)
```

## Coverage Badge
```markdown
[![coverage report](https://gitlab.com/your-namespace/plusparser/badges/main/coverage.svg)](https://gitlab.com/your-namespace/plusparser/-/commits/main)
```

## Combined Example
```markdown
# Plus Parser

[![pipeline status](https://gitlab.com/your-namespace/plusparser/badges/main/pipeline.svg)](https://gitlab.com/your-namespace/plusparser/-/commits/main)
[![coverage report](https://gitlab.com/your-namespace/plusparser/badges/main/coverage.svg)](https://gitlab.com/your-namespace/plusparser/-/commits/main)

A Spring Boot application for parsing and extracting content from URLs.
```

## Custom Badge URLs

Replace `your-namespace` with your actual GitLab namespace/username:

- **Pipeline**: `https://gitlab.com/your-namespace/plusparser/badges/main/pipeline.svg`
- **Coverage**: `https://gitlab.com/your-namespace/plusparser/badges/main/coverage.svg`

## Badge Styles

You can customize badge appearance by adding parameters:

```markdown
<!-- Flat style -->
[![pipeline status](https://gitlab.com/your-namespace/plusparser/badges/main/pipeline.svg?style=flat)](https://gitlab.com/your-namespace/plusparser/-/commits/main)

<!-- Flat square style -->
[![coverage report](https://gitlab.com/your-namespace/plusparser/badges/main/coverage.svg?style=flat-square)](https://gitlab.com/your-namespace/plusparser/-/commits/main)
```

## Branch-Specific Badges

For different branches, replace `main` with the branch name:

```markdown
<!-- Development branch -->
[![pipeline status](https://gitlab.com/your-namespace/plusparser/badges/develop/pipeline.svg)](https://gitlab.com/your-namespace/plusparser/-/commits/develop)
```
