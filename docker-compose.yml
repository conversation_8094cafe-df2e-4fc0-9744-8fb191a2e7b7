# Docker Compose configuration for Transaction Store
# This configuration demonstrates secure management port handling

version: '3.8'

networks:
  # Public network for application traffic
  public:
    driver: bridge
  
  # Private network for monitoring (optional)
  monitoring:
    driver: bridge
    internal: true  # No external internet access

services:
  # Main application
  transactionstore:
    build: .
    container_name: transactionstore-app
    networks:
      - public
      - monitoring
    ports:
      # Only expose application port publicly
      - "8080:8080"
      # Management port 8081 is NOT exposed - stays internal
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - SPRING_DATASOURCE_URL=************************************************
      - SPRING_DATASOURCE_USERNAME=transactionstore
      - SPRING_DATASOURCE_PASSWORD=password
    depends_on:
      - postgres
    healthcheck:
      # Health check works internally even without exposing port
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Database
  postgres:
    image: postgres:15-alpine
    container_name: transactionstore-db
    networks:
      - monitoring  # Only on private network
    environment:
      - POSTGRES_DB=transactionstore
      - POSTGRES_USER=transactionstore
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      # Database not exposed externally for security
      - "127.0.0.1:5432:5432"  # Only localhost access

  # Optional: Monitoring stack (can access management port internally)
  prometheus:
    image: prom/prometheus:latest
    container_name: transactionstore-prometheus
    networks:
      - monitoring  # Only on monitoring network
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: transactionstore-grafana
    networks:
      - monitoring
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  postgres_data:
  grafana_data:

# Example usage:
# 
# Start services:
#   docker-compose up -d
#
# Access application:
#   curl http://localhost:8080/api/v1/accounts
#
# Access monitoring (if enabled):
#   http://localhost:9090 (Prometheus)
#   http://localhost:3000 (Grafana)
#
# Management endpoints are NOT accessible externally:
#   curl http://localhost:8081/actuator/health  # This will FAIL
#
# But they work internally for health checks and monitoring
