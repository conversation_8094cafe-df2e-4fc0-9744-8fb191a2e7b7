package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.ParticipantsApi;
import com.mymueller.transactionsstore.model.Participants;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * Implementation of the generated ParticipantsApi interface
 * This is a simple in-memory implementation for demonstration purposes
 */
@RestController
public class ParticipantsController implements ParticipantsApi {

    @Override
    public ResponseEntity<List<Participants>> getParticipants() {
        return new ResponseEntity<>(Collections.emptyList(), HttpStatus.OK);
    }
}
