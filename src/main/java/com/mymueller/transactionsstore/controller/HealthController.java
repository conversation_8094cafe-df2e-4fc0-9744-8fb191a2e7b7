package com.mymueller.transactionsstore.controller;

import com.mymueller.transactionsstore.api.HealthApi;
import com.mymueller.transactionsstore.model.HealthCheck200Response;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.time.OffsetDateTime;

/**
 * Simple health check controller implementation
 */
@RestController
public class HealthController implements HealthApi {

    @Override
    public ResponseEntity<HealthCheck200Response> healthCheck() {
        HealthCheck200Response response = new HealthCheck200Response();
        response.setStatus("OK");
        response.setTimestamp(OffsetDateTime.now());
        
        return ResponseEntity.ok(response);
    }
}
