package com.mymueller.clipstorage.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.UUID;

public class TextLine {
    private final String id;
    private final String content;
    private final boolean isSeparator;

    public TextLine(final String content)
    {
        this(false, content);
    }

    public TextLine(final boolean isSeparator, final String content)
    {
        this.id = UUID.randomUUID().toString();
        this.content = content;
        this.isSeparator = isSeparator;
    }

    // Add this constructor for Jackson deserialization
    @JsonCreator
    public TextLine(
            @JsonProperty("id") final String id,
            @JsonProperty("content") final String content,
            @JsonProperty("separator") final boolean isSeparator)
    {
        this.id = id;
        this.content = content;
        this.isSeparator = isSeparator;
    }

    public String getId() {
        return id;
    }
    
    public String getContent() {
        return content;
    }

    public boolean isSeparator()
    {
        return isSeparator;
    }
}