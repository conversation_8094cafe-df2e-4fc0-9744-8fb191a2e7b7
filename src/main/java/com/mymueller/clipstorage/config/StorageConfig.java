package com.mymueller.clipstorage.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Path;
import java.nio.file.Paths;

@Configuration
@ConfigurationProperties(prefix = "clipstorage.storage")
public class StorageConfig {
    private boolean enabled = true;
    private String directory;
    private String filename = "textlines.json";
    private BackupConfig backup = new BackupConfig();

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(final boolean enabled) {
        this.enabled = enabled;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(final String directory) {
        this.directory = directory;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(final String filename) {
        this.filename = filename;
    }

    public BackupConfig getBackup() {
        return backup;
    }

    public void setBackup(final BackupConfig backup) {
        this.backup = backup;
    }

    public Path getFullPath() {
        return Paths.get(directory, filename);
    }

    public static class BackupConfig {
        private boolean enabled = true;
        private int count = 5;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(final boolean enabled) {
            this.enabled = enabled;
        }

        public int getCount() {
            return count;
        }

        public void setCount(final int count) {
            this.count = count;
        }
    }
}