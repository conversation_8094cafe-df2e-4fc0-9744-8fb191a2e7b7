package com.mymueller.clipstorage.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.clipstorage.config.StorageConfig;
import com.mymueller.clipstorage.model.TextLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

@Service
public class StorageService {
    private static final Logger logger = LoggerFactory.getLogger(StorageService.class);
    private final StorageConfig storageConfig;
    private final ObjectMapper objectMapper;

    public StorageService(final StorageConfig storageConfig, final ObjectMapper objectMapper) {
        this.storageConfig = storageConfig;
        this.objectMapper = objectMapper;
        initializeStorage();
    }

    private void initializeStorage() {
        if (!storageConfig.isEnabled()) {
            logger.info("Storage is disabled");
            return;
        }

        try {
            final Path directory = Path.of(storageConfig.getDirectory());
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                logger.info("Created storage directory: {}", directory);
            }
        } catch (final IOException e) {
            logger.error("Failed to initialize storage directory", e);
            storageConfig.setEnabled(false);
        }
    }

    public void saveTextLines(final List<TextLine> textLines) {
        if (!storageConfig.isEnabled()) {
            return;
        }

        try {
            final Path filePath = storageConfig.getFullPath();
            
            // Create backup if enabled
            if (storageConfig.getBackup().isEnabled() && Files.exists(filePath)) {
                createBackup(filePath);
            }
            
            // Enable pretty printing
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(filePath.toFile(), textLines);
            logger.info("Saved {} text lines to {}", textLines.size(), filePath);
        } catch (final IOException e) {
            logger.error("Failed to save text lines", e);
        }
    }

    public List<TextLine> loadTextLines() {
        if (!storageConfig.isEnabled()) {
            return new ArrayList<>();
        }

        final Path filePath = storageConfig.getFullPath();
        if (!Files.exists(filePath)) {
            logger.info("Storage file does not exist: {}", filePath);
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(
                filePath.toFile(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, TextLine.class)
            );
        } catch (final IOException e) {
            logger.error("Failed to load text lines", e);
            return new ArrayList<>();
        }
    }

    private void createBackup(final Path filePath) throws IOException {
        final int backupCount = storageConfig.getBackup().getCount();
        
        // Shift existing backups
        for (int i = backupCount - 1; i > 0; i--) {
            final Path backupPath = getBackupPath(filePath, i);
            final Path previousBackupPath = getBackupPath(filePath, i - 1);
            
            if (Files.exists(previousBackupPath)) {
                Files.move(previousBackupPath, backupPath, StandardCopyOption.REPLACE_EXISTING);
            }
        }
        
        // Create new backup
        final Path firstBackupPath = getBackupPath(filePath, 1);
        Files.copy(filePath, firstBackupPath, StandardCopyOption.REPLACE_EXISTING);
    }

    private Path getBackupPath(final Path filePath, final int backupNumber) {
        return filePath.resolveSibling(filePath.getFileName() + "." + backupNumber + ".bak");
    }
}