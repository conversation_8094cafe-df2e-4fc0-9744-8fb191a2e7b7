package com.mymueller.clipstorage.textline;

import com.mymueller.clipstorage.service.TextLineService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class TextLineController {

    private final TextLineService textLineService;

    public TextLineController(final TextLineService textLineService)
    {
        this.textLineService = textLineService;
    }

    @GetMapping("/")
    public String index(final Model model)
    {
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "index";
    }

    @PostMapping("/add")
    public String addTextLine(@RequestParam final String content, final Model model)
    {
        textLineService.addTextLine(content);
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "fragments :: textLinesList";
    }

    @PostMapping("/delete")
    public String deleteTextLine(@RequestParam final String id, final Model model)
    {
        textLineService.deleteTextLine(id);
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "fragments :: textLinesList";
    }

    @PostMapping("/add-separator")
    public String addSeparator(@RequestParam final String content, final Model model)
    {
        textLineService.addSeparator(content);
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "fragments :: textLinesList";
    }

    /**
     * Refreshes the UI list without reloading from persistence.
     */
    @PostMapping("/refresh")
    public String refresh(final Model model)
    {
        // Just return the current list without reloading from storage
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "fragments :: textLinesList";
    }

    /**
     * Reorders a text line by moving it from one position to another.
     *
     * @param sourceId The ID of the text line to move
     * @param targetId The ID of the text line to move it before/after
     * @param position Either "before" or "after" to indicate where to place the source relative to the target
     * @param model The model to add attributes to
     * @return The updated text lines fragment
     */
    @PostMapping("/reorder")
    public String reorder(
            @RequestParam final String sourceId,
            @RequestParam final String targetId,
            @RequestParam final String position,
            final Model model)
    {
        textLineService.reorder(sourceId, targetId, position);
        model.addAttribute("textLines", textLineService.getAllTextLines());
        return "fragments :: textLinesList";
    }
}
