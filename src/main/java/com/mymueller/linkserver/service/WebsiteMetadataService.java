package com.mymueller.linkserver.service;

import com.mymueller.linkserver.model.Bookmark;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class WebsiteMetadataService {
    
    private static final Logger logger = LoggerFactory.getLogger(WebsiteMetadataService.class);
    private static final int TIMEOUT_MILLIS = 10000; // 10 seconds
    
    /**
     * Fetches metadata from a website URL
     * 
     * @param url The URL to fetch metadata from
     * @return A BookmarkMetadata object containing the extracted information
     */
    public BookmarkMetadata fetchMetadata(String url) {
        BookmarkMetadata metadata = new BookmarkMetadata();
        metadata.setUrl(url);
        
        try {
            Document doc = Jsoup.connect(url)
                    .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                    .timeout(TIMEOUT_MILLIS)
                    .get();
            
            // Extract title
            String title = doc.title();
            if (title != null && !title.isEmpty()) {
                metadata.setTitle(title);
            } else {
                metadata.setTitle(url);
            }
            
            // Extract description
            String description = doc.select("meta[name=description]").attr("content");
            if (description.isEmpty()) {
                description = doc.select("meta[property=og:description]").attr("content");
            }
            metadata.setDescription(description);
            
            // Determine content type
            metadata.setContentType(determineContentType(url, doc));
            
            // Extract potential categories
            metadata.setSuggestedCategories(extractPotentialCategories(doc));
            
        } catch (IOException e) {
            logger.error("Error fetching metadata from URL: " + url, e);
            metadata.setTitle(url);
            metadata.setContentType(Bookmark.ContentType.OTHER);
        }
        
        return metadata;
    }
    
    /**
     * Determines the content type based on URL and document content
     */
    private Bookmark.ContentType determineContentType(String url, Document doc) {
        // Check for video content
        if (url.contains("youtube.com") || url.contains("vimeo.com") || 
            url.contains("dailymotion.com") || doc.select("meta[property=og:video]").size() > 0) {
            return Bookmark.ContentType.VIDEO;
        }
        
        // Check for image content
        if (url.matches(".*\\.(jpg|jpeg|png|gif|bmp|svg)$") || 
            doc.select("meta[property=og:image]").size() > 0 ||
            url.contains("flickr.com") || url.contains("imgur.com")) {
            return Bookmark.ContentType.IMAGE;
        }
        
        // Check for document content
        if (url.matches(".*\\.(pdf|doc|docx|ppt|pptx|xls|xlsx|txt)$") ||
            url.contains("docs.google.com") || url.contains("github.com")) {
            return Bookmark.ContentType.DOCUMENT;
        }
        
        // Default to article for most web pages
        return Bookmark.ContentType.ARTICLE;
    }
    
    /**
     * Extracts potential categories from the document
     */
    private List<String> extractPotentialCategories(Document doc) {
        List<String> categories = new ArrayList<>();
        
        // Extract from meta keywords
        String keywords = doc.select("meta[name=keywords]").attr("content");
        if (!keywords.isEmpty()) {
            for (String keyword : keywords.split(",")) {
                String trimmed = keyword.trim();
                if (!trimmed.isEmpty() && trimmed.length() < 30) {
                    categories.add(trimmed);
                }
            }
        }
        
        // Extract from article tags if present
        doc.select("meta[property=article:tag]").forEach(tag -> {
            String content = tag.attr("content").trim();
            if (!content.isEmpty() && content.length() < 30) {
                categories.add(content);
            }
        });
        
        return categories;
    }
    
    /**
     * Class to hold website metadata
     */
    public static class BookmarkMetadata {
        private String url;
        private String title;
        private String description;
        private Bookmark.ContentType contentType;
        private List<String> suggestedCategories = new ArrayList<>();
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public Bookmark.ContentType getContentType() {
            return contentType;
        }
        
        public void setContentType(Bookmark.ContentType contentType) {
            this.contentType = contentType;
        }
        
        public List<String> getSuggestedCategories() {
            return suggestedCategories;
        }
        
        public void setSuggestedCategories(List<String> suggestedCategories) {
            this.suggestedCategories = suggestedCategories;
        }
    }
}
