package com.mymueller.linkserver.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/theme")
public class ThemeController {

    @GetMapping("/switch")
    @ResponseBody
    public String switchTheme(@RequestParam("theme") String theme, HttpServletRequest request) {
        HttpSession session = request.getSession();
        session.setAttribute("theme", theme);
        return "{\"success\": true, \"theme\": \"" + theme + "\"}";
    }
}
