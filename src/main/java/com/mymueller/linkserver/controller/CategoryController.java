package com.mymueller.linkserver.controller;

import com.mymueller.linkserver.model.Category;
import com.mymueller.linkserver.service.CategoryService;
import jakarta.validation.Valid;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/categories")
public class CategoryController {
    
    private final CategoryService categoryService;
    
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }
    
    @GetMapping
    public String listCategories(Model model) {
        List<Category> categories = categoryService.getAllCategories();
        model.addAttribute("categories", categories);
        return "categories/list";
    }
    
    @GetMapping("/create")
    public String showCreateForm(Model model) {
        model.addAttribute("category", new Category());
        return "categories/create";
    }
    
    @PostMapping("/create")
    public String createCategory(@Valid @ModelAttribute Category category, BindingResult result, Model model) {
        if (result.hasErrors()) {
            return "categories/create";
        }
        
        try {
            categoryService.createCategory(category.getName(), category.getDescription());
            return "redirect:/categories";
        } catch (RuntimeException e) {
            model.addAttribute("error", e.getMessage());
            return "categories/create";
        }
    }
    
    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model) {
        Category category = categoryService.findById(id)
                .orElseThrow(() -> new RuntimeException("Category not found"));
        
        model.addAttribute("category", category);
        return "categories/edit";
    }
    
    @PostMapping("/edit/{id}")
    public String updateCategory(@PathVariable Long id, @Valid @ModelAttribute Category category,
                               BindingResult result, Model model) {
        if (result.hasErrors()) {
            return "categories/edit";
        }
        
        try {
            categoryService.updateCategory(id, category.getName(), category.getDescription());
            return "redirect:/categories";
        } catch (RuntimeException e) {
            model.addAttribute("error", e.getMessage());
            return "categories/edit";
        }
    }
    
    @GetMapping("/delete/{id}")
    public String deleteCategory(@PathVariable Long id) {
        categoryService.deleteCategory(id);
        return "redirect:/categories";
    }
}
