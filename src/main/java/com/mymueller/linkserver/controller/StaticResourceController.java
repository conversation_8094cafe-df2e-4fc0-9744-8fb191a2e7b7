package com.mymueller.linkserver.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@Controller
public class StaticResourceController {

    @GetMapping("/css/{fileName:.+}")
    @ResponseBody
    public ResponseEntity<String> getCssFile(@PathVariable String fileName) throws IOException {
        Resource resource = new ClassPathResource("static/css/" + fileName);
        String content = new String(Files.readAllBytes(resource.getFile().toPath()), StandardCharsets.UTF_8);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("text/css"))
                .body(content);
    }

    @GetMapping("/js/{fileName:.+}")
    @ResponseBody
    public ResponseEntity<String> getJsFile(@PathVariable String fileName) throws IOException {
        Resource resource = new ClassPathResource("static/js/" + fileName);
        String content = new String(Files.readAllBytes(resource.getFile().toPath()), StandardCharsets.UTF_8);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/javascript"))
                .body(content);
    }
}
