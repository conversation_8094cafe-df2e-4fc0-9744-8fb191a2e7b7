package com.example.aiserv.services;

import com.example.aiserv.ai.AIRequestBody;
import com.example.aiserv.ai.ChatService;
import com.example.aiserv.ai.OllamaChatService;
import com.example.aiserv.model.AIRequest;
import com.example.aiserv.model.AIResult;
import com.example.aiserv.persist.ResultRepository;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class AIService
{
    private final ChatService processTextService;

    private final ResultRepository<Integer, AIResult> resultRepository;

    private final Queue<AIRequest> requestQueue;

    private final Executor executor;

    private final AtomicInteger counter = new AtomicInteger(0);
//    private final ChatModelPromptContentObservationFilter chatModelPromptContentObservationFilter;

    public AIService(final ChatService processTextService,
                     final ResultRepository<Integer, AIResult> resultRepository)
    {
        this.processTextService = processTextService;
        this.resultRepository = resultRepository;
        requestQueue = new ConcurrentLinkedQueue<>();

        executor = Executors.newFixedThreadPool(1);
    }

    public void processRequest(final AIRequest request)
    {
        requestQueue.add(request);

        processRequests();
    }

    public String processSyncRequest(final String userPrompt)
    {
        final var response = processTextService.sendMessage(userPrompt, null);

        System.err.println("Sync request: " + userPrompt + " -> " + response);

        return response;
    }

    public String processSyncRequest(final AIRequestBody prompt)
    {
        final var response = processTextService.sendImageMessage(prompt.getPrompt(), prompt.getImage());

        System.err.println("Sync request: " + prompt + " -> " + response);

        return response;
    }

    private void processRequests()
    {
        final Runnable action = () -> {
            final var request = requestQueue.poll();
            if (null != request) {
                final AIResult result = switch (request.type())
                {
                    case "text" ->
                    {
                        final var response = processTextService.sendMessage(request.userPrompt(), request.systemPrompt());

                        yield new AIResult(UUID.randomUUID().toString(), request, response, Instant.now());
                    }
                    default ->
                    {
                        System.err.println("Unknown request type: " + request.type());
                        yield null;
                    }
                };

                if (null != result) {
                    resultRepository.save(counter.incrementAndGet(), result);
                }
            }
        };

        executor.execute(action);
    }
}
