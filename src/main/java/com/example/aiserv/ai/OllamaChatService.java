package com.example.aiserv.ai;

import dev.langchain4j.model.chat.ChatLanguageModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Clock;
import java.time.Instant;

//@Service
public class OllamaChatService implements ChatService
{

    private static final Logger logger = LoggerFactory.getLogger(OllamaChatService.class);


    private final ChatLanguageModel model;

    public OllamaChatService(final ChatLanguageModel model)
    {
        this.model = model;
    }

    public String sendMessage(final String userPrompt, final String systemPrompt)
    {
        logger.debug("Send prompt: {} ({})", userPrompt, Instant.now(Clock.systemDefaultZone()));

        final long start = System.currentTimeMillis();

        try
        {
            final String response = model.chat(userPrompt);

            final long runtime = System.currentTimeMillis() - start;
            logger.info("Prompt response received after {} ms", runtime);
            return response;
        }
        catch (final Exception e)
        {
            logger.error("Error occurred while processing the prompt", e);
            return "An error occurred while processing your request. Please try again.";
        }
    }

    @Override
    public String sendImageMessage(final String userPrompt, final String image)
    {
        return "Unimplemented " + getClass().getSimpleName();
    }
}
