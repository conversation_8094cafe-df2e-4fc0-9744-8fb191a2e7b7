package com.example.aiserv.ai;

import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.CustomMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.model.output.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Clock;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.lang.Thread.sleep;

public class OllamaStreamingChatService implements ChatService
{

    private static final Logger logger = LoggerFactory.getLogger(OllamaStreamingChatService.class);


    private final StreamingChatLanguageModel model;

    public OllamaStreamingChatService(final StreamingChatLanguageModel model)
    {
        this.model = model;
    }

    public String sendMessage(final String userPrompt, final String systemPrompt)
    {
//        logger.debug("Send prompt: {} ({})", userPrompt, Instant.now(Clock.systemDefaultZone()));
//
//        final long start = System.currentTimeMillis();
//
//        final StringBuilder buffer = new StringBuilder();
//
//        model.chat(userPrompt, new StreamingChatResponseHandler() {
//            @Override
//            public void onPartialResponse(final String token) {
//                System.out.println(token);
//            }
//
//            @Override
//            public void onCompleteResponse(final ChatResponse response) {
//                System.out.println("\nResponse complete: " + response.aiMessage().text());
//                buffer.append(response.aiMessage().text());
//            }
//
//            @Override
//            public void onError(final Throwable error) {
//                logger.error("Failed to process prompt", error);
//                buffer.append("An error occurred while processing your request: ").append(error.getMessage());
//            }
//        });
//
//        while (buffer.isEmpty()) {
//            try
//            {
//                sleep(10);
//            }
//            catch (InterruptedException e)
//            {
////                throw new RuntimeException(e);
//                buffer.append("An error occurred while processing your request: ").append(e.getMessage());
//            }
//        }
//
//        return buffer.toString();

        return sendImageMessage(userPrompt, null);
    }

    @Override
    public String sendImageMessage(final String userPrompt, final String image)
    {
        logger.debug("Send prompt: {} ({})", userPrompt, Instant.now(Clock.systemDefaultZone()));

        final long start = System.currentTimeMillis();

        final StringBuilder buffer = new StringBuilder();

        final List<ChatMessage> messages = new ArrayList<>();
        messages.add(    new UserMessage(userPrompt));
        if (null != image)
        {
            messages.add(CustomMessage.from(Map.of("images", List.of(image))));
        }

        model.chat(ChatRequest.builder().messages(messages).build(), new StreamingChatResponseHandler() {
            @Override
            public void onPartialResponse(final String token) {
                logger.debug("Token: {}", token);
            }

            @Override
            public void onCompleteResponse(final ChatResponse response) {
                logger.info("Response complete after {} ms: {}",  System.currentTimeMillis() - start, response.aiMessage().text());
                buffer.append(response.aiMessage().text());
            }

            @Override
            public void onError(final Throwable error) {
                logger.error("Failed to process prompt after {} ms", System.currentTimeMillis() - start, error);
                buffer.append("An error occurred while processing your request: ").append(error.getMessage());
            }
        });

        while (buffer.isEmpty()) {
            try
            {
                sleep(10);
            }
            catch (final InterruptedException e)
            {
                buffer.append("An error occurred while processing your request: ").append(e.getMessage());
            }
        }

        return buffer.toString();
    }
}
