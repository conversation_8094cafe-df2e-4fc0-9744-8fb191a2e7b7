package com.example.aiserv.ai;

import org.springframework.stereotype.Service;

@Service
public class ImageService
{
//    private final ImageModel model;
//
//    private final ImageOptions options;
//
//    public ImageService(final ImageModel model, final ImageOptions options)
//    {
//        this.model = model;
//        this.options = options;
//    }
//
//    public String sendMessage(String userPrompt, String systemPrompt)
//    {
//        System.out.println("Send image prompt: " + userPrompt + " (" + Instant.now(Clock.systemDefaultZone()) + ")");
//        long start = System.currentTimeMillis();
//
//        final ImagePrompt requestPrompt;
//        if (null != systemPrompt && !systemPrompt.trim().isEmpty())
//        {
//            requestPrompt = new ImagePrompt(List.of(new ImageMessage(userPrompt), new ImageMessage(systemPrompt)));
////            requestPrompt = new Prompt(List.of(new UserMessage(userPrompt), new SystemMessage(systemPrompt)), options);
//        }
//        else
//        {
//            requestPrompt = new ImagePrompt(userPrompt);
////            requestPrompt = new Prompt(List.of(new UserMessage(userPrompt)), options);
//        }
//
//        String response = model.call(requestPrompt).getResult().getOutput().getUrl();
//
//        long runtime = System.currentTimeMillis() - start;
//        System.err.println("!!! Prompt response after " + runtime + "ms");
//        System.err.println("#######################");
//        return response;
//    }
}
