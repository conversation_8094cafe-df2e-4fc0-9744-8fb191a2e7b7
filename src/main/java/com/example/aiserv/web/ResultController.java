package com.example.aiserv.web;

import com.example.aiserv.model.AIResult;
import com.example.aiserv.persist.ResultRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@RestController
public class ResultController
{

    private final ResultRepository<Integer, AIResult> resultRepository;
    private final AIResult NOT_FOUND = new AIResult("0", null, "Not found", Instant.EPOCH);

    public ResultController(final ResultRepository<Integer, AIResult> resultRepository)
    {
        this.resultRepository = Objects.requireNonNull(resultRepository, "resultRepository must not be null");
    }

    @GetMapping("/results")
    public List<Integer> getAllResults()
    {
        var resultKeys = new ArrayList<>(resultRepository.findAllKeys());
        resultKeys.sort(Collections.reverseOrder());
        return resultKeys;
    }

    @GetMapping(path = "/results/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AIResult> getResultById(@PathVariable("id") Integer id)
    {
        return resultRepository.findById(id)
                               .map(ResponseEntity::ok)
                               .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(NOT_FOUND));
    }

    @GetMapping(path = "/results/{id}/text", produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> getResultTextById(@PathVariable("id") Integer id)
    {
        return resultRepository.findById(id)
                               .map(result -> ResponseEntity.ok(result.result()))
                               .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(NOT_FOUND.result()));
    }

    @DeleteMapping("/results/{id}")
    public ResponseEntity<Void> deleteResultById(@PathVariable("id") Integer id)
    {
        if (resultRepository.delete(id))
        {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        } else
        {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}
