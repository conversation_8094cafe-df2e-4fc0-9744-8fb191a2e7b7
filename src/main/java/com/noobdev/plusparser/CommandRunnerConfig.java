package com.noobdev.plusparser;

import com.noobdev.plusparser.service.URLProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class CommandRunnerConfig
{
    private final URLProcessingService urlProcessingService;

    @Autowired
    public CommandRunnerConfig(final URLProcessingService urlProcessingService)
    {
        this.urlProcessingService = urlProcessingService;
    }

    @Bean
    public CommandLineRunner commandLineRunner(final ApplicationContext ctx)
    {
        return args -> {
            // Only process URLs if arguments are provided
            if (args.length > 0) {
                processURLs(args);
            } else {
                System.out.println("Starting in web mode. Access the application at http://localhost:7071");
            }
        };
    }

    private void processURLs(final String[] urls)
    {
        if (urls.length == 0) {
            System.err.println("Usage: java PlusParser.jar <url>");
        } else {
            Arrays.stream(urls).map(urlProcessingService::processSingleURL).forEach(System.out::println);
        }
    }
}