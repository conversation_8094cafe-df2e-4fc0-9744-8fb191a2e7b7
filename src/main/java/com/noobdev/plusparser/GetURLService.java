package com.noobdev.plusparser;

import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;

@Service
public class GetURLService
{
    public URL getURL(String uri)
    {
        final URL result;
        try
        {
            result = new URIL(uri).toURL();
        }
        catch (MalformedURLException e)
        {
            throw new RuntimeException(e);
        }

        return result;
    }
}
