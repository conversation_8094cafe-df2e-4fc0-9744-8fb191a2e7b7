package com.noobdev.plusparser.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.noobdev.plusparser.GetURLContent;
import com.noobdev.plusparser.GetURLService;
import com.noobdev.plusparser.PlusJSonExtrator;
import com.noobdev.plusparser.model.Article;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Optional;

@Service
public class URLProcessingService {

    private final GetURLService uriService;
    private final GetURLContent urlContent;
    private final PlusJSonExtrator extractor;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    public URLProcessingService(final GetURLService uriService, final GetURLContent urlContent, final PlusJSonExtrator extractor) {
        this.uriService = uriService;
        this.urlContent = urlContent;
        this.extractor = extractor;
    }

    public String processSingleURL(final String url) {
        return Optional.ofNullable(url)
                .map(uriService::getURL)
                .map(urlContent::getURLContent)
                .map(extractor::extractJson)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(this::convert2Article)
                .map(this::article2Text)
                .orElse(null);
    }

    private Article convert2Article(final String s) {
        try {
            return objectMapper.readValue(s, Article.class);
        } catch (final IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private String article2Text(final Article article) {
        final StringBuilder textBuilder = new StringBuilder();

        textBuilder.append("Headline: ").append(article.headline()).append("\n").append("\n");
        textBuilder.append("Author: ");
        article.
                authors().
                stream().forEach(a -> textBuilder.append(a.name()).append("  "));
        textBuilder.append("\n\n");

        article.elements().stream()
                .filter(element -> "text".equals(element.type()))
                .forEach(element -> textBuilder.append(element.text()).append("\n\n"));

        return textBuilder.toString();
    }
}
