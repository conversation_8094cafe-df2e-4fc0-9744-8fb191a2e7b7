<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/main :: html(content=~{::content})}">
<head>
    <meta charset="UTF-8">
    <title>LinkServer - Categories</title>
</head>
<body>
    <div th:replace="~{layout/main :: body(content=~{::content})}">
        <div th:fragment="content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Categories</h2>
                <a th:href="@{/categories/create}" class="btn btn-primary">Add Category</a>
            </div>

            <div class="row" th:if="${categories.empty}">
                <div class="col">
                    <div class="alert alert-info">
                        No categories found. <a th:href="@{/categories/create}">Create your first category</a>.
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="category : ${categories}">
                                            <td th:text="${category.name}"></td>
                                            <td th:text="${category.description}"></td>
                                            <td>
                                                <a th:href="@{/bookmarks/filter/category/{id}(id=${category.id})}" class="btn btn-sm btn-outline-primary">View Bookmarks</a>
                                                <a th:href="@{/categories/edit/{id}(id=${category.id})}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                                <a th:href="@{/categories/delete/{id}(id=${category.id})}" class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('Are you sure you want to delete this category?')">Delete</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
