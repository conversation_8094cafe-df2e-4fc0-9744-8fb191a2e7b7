<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
    <div id="result-container" class="result-section">
        <div class="result-header">
            <h3 th:if="${success}" class="success-header">✅ Processing Complete</h3>
            <h3 th:unless="${success}" class="error-header">❌ Processing Failed</h3>
            <p class="processed-url">URL: <span th:text="${url}"></span></p>
        </div>
        
        <div class="form-group">
            <label for="content">Processed Content:</label>
            <textarea id="content" readonly th:text="${processedContent}" 
                      th:class="${success} ? 'content-success' : 'content-error'"></textarea>
        </div>
        
        <div class="result-actions">
            <button type="button" onclick="copyToClipboard()" class="copy-btn">📋 Copy Content</button>
            <button type="button" onclick="clearResults()" class="clear-btn">🗑️ Clear Results</button>
        </div>
    </div>
</body>
</html>
