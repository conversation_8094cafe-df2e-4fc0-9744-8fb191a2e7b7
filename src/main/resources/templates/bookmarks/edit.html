<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/main :: html(content=~{::content})}">
<head>
    <meta charset="UTF-8">
    <title>LinkServer - Edit Bookmark</title>
</head>
<body>
    <div th:replace="~{layout/main :: body(content=~{::content})}">
        <div th:fragment="content">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>Edit Bookmark</h3>
                        </div>
                        <div class="card-body">
                            <form th:action="@{/bookmarks/edit/{id}(id=${bookmark.id})}" th:object="${bookmark}" method="post">
                                <input type="hidden" th:field="*{id}">
                                <div class="mb-3">
                                    <label for="url" class="form-label">URL</label>
                                    <input type="url" class="form-control" id="url" th:field="*{url}" required>
                                    <div class="text-danger" th:if="${#fields.hasErrors('url')}" th:errors="*{url}"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="title" th:field="*{title}" required>
                                    <div class="text-danger" th:if="${#fields.hasErrors('title')}" th:errors="*{title}"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                                    <div class="text-danger" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="contentType" class="form-label">Content Type</label>
                                    <select class="form-select" id="contentType" th:field="*{contentType}" required>
                                        <option value="">Select a content type</option>
                                        <option th:each="type : ${contentTypes}" th:value="${type}" th:text="${type}"></option>
                                    </select>
                                    <div class="text-danger" th:if="${#fields.hasErrors('contentType')}" th:errors="*{contentType}"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="categoryNames" class="form-label">Categories</label>
                                    <input type="text" class="form-control" id="categoryNames" name="categoryNames" th:value="${categoryNames}" placeholder="Enter categories separated by commas">
                                    <div class="form-text">Enter new categories or choose from existing ones:</div>
                                    <div class="d-flex flex-wrap mt-2">
                                        <span th:each="category : ${categories}" class="badge bg-secondary category-badge"
                                              style="cursor: pointer;"
                                              onclick="addCategory(this.innerText)"
                                              th:text="${category.name}"></span>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <a th:href="@{/bookmarks}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Update Bookmark</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function addCategory(categoryName) {
                    const input = document.getElementById('categoryNames');
                    const currentValue = input.value.trim();

                    if (currentValue === '') {
                        input.value = categoryName;
                    } else {
                        const categories = currentValue.split(',').map(cat => cat.trim());
                        if (!categories.includes(categoryName)) {
                            input.value = currentValue + ', ' + categoryName;
                        }
                    }
                }
            </script>
        </div>
    </div>
</body>
</html>
