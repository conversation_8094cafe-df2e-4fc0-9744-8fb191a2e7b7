<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>LinkServer - Filtered Bookmarks</title>
</head>
<body>
    <div th:replace="~{layout/main :: body(content=~{::content})}">
        <div th:fragment="content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Bookmarks filtered by <span th:text="${filterType}"></span>: <span th:text="${filterValue}"></span></h2>
                <a th:href="@{/bookmarks}" class="btn btn-secondary">Back to All Bookmarks</a>
            </div>
            
            <div class="row" th:if="${bookmarks.empty}">
                <div class="col">
                    <div class="alert alert-info">
                        No bookmarks found with this filter.
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4" th:each="bookmark : ${bookmarks}">
                    <div class="card bookmark-card h-100">
                        <div class="card-body">
                            <h5 class="card-title" th:text="${bookmark.title}"></h5>
                            <h6 class="card-subtitle mb-2 text-muted text-truncate" th:text="${bookmark.url}"></h6>
                            <p class="card-text" th:if="${bookmark.description}" th:text="${bookmark.description}"></p>
                            <div class="mb-2">
                                <span class="badge bg-primary" th:text="${bookmark.contentType}"></span>
                                <span class="badge bg-secondary category-badge" th:each="category : ${bookmark.categories}" th:text="${category.name}"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a th:href="${bookmark.url}" target="_blank" class="btn btn-sm btn-outline-primary">Visit</a>
                                <div>
                                    <a th:href="@{/bookmarks/edit/{id}(id=${bookmark.id})}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                    <a th:href="@{/bookmarks/delete/{id}(id=${bookmark.id})}" class="btn btn-sm btn-outline-danger" 
                                       onclick="return confirm('Are you sure you want to delete this bookmark?')">Delete</a>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-muted">
                            <small th:text="${#temporals.format(bookmark.createdAt, 'dd MMM yyyy')}"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
