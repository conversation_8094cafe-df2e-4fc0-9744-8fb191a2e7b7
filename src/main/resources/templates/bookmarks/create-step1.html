<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>LinkServer - Add Bookmark (Step 1)</title>
</head>
<body>
    <div th:replace="~{layout/main :: body(content=~{::content})}">
        <div th:fragment="content">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>Add New Bookmark - Step 1</h3>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">Enter the URL of the website you want to bookmark. We'll fetch the title, description, and suggest categories for you.</p>
                            
                            <form th:action="@{/bookmarks/create/fetch-metadata}" th:object="${bookmark}" method="post">
                                <div class="mb-3">
                                    <label for="url" class="form-label">URL</label>
                                    <input type="text" class="form-control" id="url" th:field="*{url}" 
                                           placeholder="Enter website URL (e.g., https://example.com)" required autofocus>
                                    <div class="form-text">If you don't include http:// or https://, we'll add it for you.</div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <a th:href="@{/bookmarks}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <span class="spinner-border spinner-border-sm d-none" id="loading-spinner" role="status" aria-hidden="true"></span>
                                        Next
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                document.querySelector('form').addEventListener('submit', function() {
                    document.getElementById('loading-spinner').classList.remove('d-none');
                    document.querySelector('button[type="submit"]').setAttribute('disabled', 'disabled');
                    document.querySelector('button[type="submit"]').innerHTML = 
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Fetching data...';
                });
            </script>
        </div>
    </div>
</body>
</html>
