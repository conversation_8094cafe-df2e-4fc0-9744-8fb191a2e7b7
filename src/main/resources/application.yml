spring:
  application:
    name: aiservls
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  ai:
    ollama:
      base-url: ${AI_OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
#          model: codellama:13b-instruct
          model: dolphin-mistral
#          model: llava:v1.6
#          model: deepseek-coder-v2:16b
#          model: qwen2.5-coder
#          model: deepseek-r1:14b
#          model: opencoder:latest
#          model: llama3.2:latest
  logging:
    level: debug

logging:
  level:
    dev:
      langchain4j: debug

server:
  port: 8080
