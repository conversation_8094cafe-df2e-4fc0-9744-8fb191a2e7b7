openapi: 3.0.3
info:
  title: Transaction Store API
  description: API for managing financial transactions
  version: 1.0.0
  contact:
    name: Transaction Store Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  # Account endpoints

  /api/v1/accounts:
    get:
      tags:
        - accounts
      summary: Get all accounts
      description: Retrieve all known accounts
      operationId: getAccounts
      responses:
        '200':
          description: List of accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Account'

  # Participants endpoints

  /api/v1/participants:
    get:
      tags:
        - participants
      summary: Get all participants
      description: Retrieve all known participants
      operationId: getParticipants
      responses:
        '200':
          description: List of participants
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Participants'


components:
  schemas:
    # Common error response schema
    ErrorResponse:
      type: object
      required:
        - message
        - timestamp
      properties:
        message:
          type: string
          description: Error message
        details:
          type: string
          description: Detailed error information
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        path:
          type: string
          description: Request path that caused the error

    # Your custom model objects will be added here step by step

    Account:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          maxLength: 150
          description: Account name
          example: "My Savings Account"

    Participants:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          maxLength: 150
          description: Participant name
          example: "John Doe"

    TransactionData:
      type: object
      required:
        - id
        - date
        - accountname
        - amount
        - participant
        - description
      properties:
        id:
          type: integer
          format: int64
          description: Unique transaction identifier
          example: 12345
        date:
          type: string
          format: date
          description: Transaction date
          example: "2024-01-15"
        accountname:
          type: string
          description: Account name
          example: "My Checking Account"
        amount:
          type: integer
          format: int64
          description: Transaction amount in cents
          example: 125050
        participant:
          type: string
          description: Transaction participant
          example: "John Doe"
        description:
          type: string
          maxLength: 200
          description: Transaction description
          example: "Payment for services"
        referenceid:
          type: integer
          format: int64
          description: Optional reference to related transaction
          example: 12344
        taxRelevant:
          type: boolean
          description: Flag to identify tax relevant transactions
          default: false
          example: true
        extraCosts:
          type: boolean
          description: Flag for extra costs
          default: false
          example: false
