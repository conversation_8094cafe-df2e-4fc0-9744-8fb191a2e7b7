openapi: 3.0.3
info:
  title: Transaction Store API
  description: API for managing financial transactions
  version: 1.0.0
  contact:
    name: Transaction Store Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  # Account endpoints

  /api/v1/accounts/{accountId}:
    get:
      tags:
        - accounts
      summary: Get account by ID
      description: Retrieve a specific account by its ID
      operationId: getAccountById
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account ID
          schema:
            type: string
            example: "acc-123"
      responses:
        '200':
          description: Account details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Account'
        '404':
          description: Account not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - accounts
      summary: Update account
      description: Update an existing account
      operationId: updateAccount
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account ID
          schema:
            type: string
            example: "acc-123"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountRequest'
      responses:
        '200':
          description: Account updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Account'
        '404':
          description: Account not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Invalid account data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    # Common error response schema
    ErrorResponse:
      type: object
      required:
        - message
        - timestamp
      properties:
        message:
          type: string
          description: Error message
        details:
          type: string
          description: Detailed error information
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        path:
          type: string
          description: Request path that caused the error

    # Your custom model objects will be added here step by step

    Account:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Unique account identifier
          example: "acc-123"
        name:
          type: string
          maxLength: 150
          description: Account name
          example: "My Savings Account"

    CreateAccountRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          maxLength: 150
          description: Account name
          example: "My Savings Account"

    UpdateAccountRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 150
          description: Account name
          example: "Updated Account Name"
