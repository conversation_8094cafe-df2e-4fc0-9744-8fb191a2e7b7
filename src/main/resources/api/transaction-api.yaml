openapi: 3.0.3
info:
  title: Transaction Store API
  description: API for managing financial transactions
  version: 1.0.0
  contact:
    name: Transaction Store Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server

paths:
  /api/v1/transactions:
    get:
      tags:
        - transactions
      summary: Get all transactions
      description: Retrieve a list of all transactions with optional filtering
      operationId: getAllTransactions
      parameters:
        - name: accountId
          in: query
          description: Filter by account ID
          required: false
          schema:
            type: string
        - name: status
          in: query
          description: Filter by transaction status
          required: false
          schema:
            $ref: '#/components/schemas/TransactionStatus'
        - name: page
          in: query
          description: Page number (0-based)
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: size
          in: query
          description: Page size
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: List of transactions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionPage'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - transactions
      summary: Create a new transaction
      description: Create a new financial transaction
      operationId: createTransaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTransactionRequest'
      responses:
        '201':
          description: Transaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '400':
          description: Invalid transaction data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/transactions/{transactionId}:
    get:
      tags:
        - transactions
      summary: Get transaction by ID
      description: Retrieve a specific transaction by its ID
      operationId: getTransactionById
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - transactions
      summary: Update transaction
      description: Update an existing transaction
      operationId: updateTransaction
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTransactionRequest'
      responses:
        '200':
          description: Transaction updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Invalid transaction data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - transactions
      summary: Delete transaction
      description: Delete a transaction by ID
      operationId: deleteTransaction
      parameters:
        - name: transactionId
          in: path
          required: true
          description: Transaction ID
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Transaction deleted successfully
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/accounts:
    get:
      tags:
        - accounts
      summary: Get all accounts
      description: Retrieve a list of all accounts
      operationId: getAllAccounts
      responses:
        '200':
          description: List of accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Account'

    post:
      tags:
        - accounts
      summary: Create a new account
      description: Create a new account
      operationId: createAccount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountRequest'
      responses:
        '201':
          description: Account created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Account'
        '400':
          description: Invalid account data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/accounts/{accountId}:
    get:
      tags:
        - accounts
      summary: Get account by ID
      description: Retrieve a specific account by its ID
      operationId: getAccountById
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account ID
          schema:
            type: string
      responses:
        '200':
          description: Account details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Account'
        '404':
          description: Account not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    Transaction:
      type: object
      required:
        - id
        - accountId
        - amount
        - description
        - status
        - date
        - reference
      properties:
        id:
          type: string
          format: uuid
          description: Unique transaction identifier
        accountId:
          type: string
          description: Account identifier
        amount:
          type: number
          format: double
          description: Transaction amount
        currency:
          type: string
          pattern: '^[A-Z]{3}$'
          description: Currency code (ISO 4217)
          example: "USD"
        description:
          type: string
          maxLength: 500
          description: Transaction description
        status:
          $ref: '#/components/schemas/TransactionStatus'
        transactionType:
          $ref: '#/components/schemas/TransactionType'
        date:
          type: string
          format: date-time
          description: Transaction creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Transaction last update timestamp
        reference:
          type: string
          maxLength: 50
          description: Transaction reference number

    CreateTransactionRequest:
      type: object
      required:
        - accountId
        - amount
        - currency
        - description
        - transactionType
      properties:
        accountId:
          type: string
          description: Account identifier
        amount:
          type: number
          format: double
          minimum: 0.01
          description: Transaction amount
        currency:
          type: string
          pattern: '^[A-Z]{3}$'
          description: Currency code (ISO 4217)
          example: "USD"
        description:
          type: string
          maxLength: 500
          description: Transaction description
        transactionType:
          $ref: '#/components/schemas/TransactionType'

    UpdateTransactionRequest:
      type: object
      properties:
        amount:
          type: number
          format: double
          minimum: 0.01
          description: Transaction amount
        description:
          type: string
          maxLength: 500
          description: Transaction description
        status:
          $ref: '#/components/schemas/TransactionStatus'

    Account:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Unique account identifier
        name:
          type: string
          maxLength: 100
          description: Account name

    CreateAccountRequest:
      type: object
      required:
        - name
        - currency
      properties:
        name:
          type: string
          maxLength: 100
          description: Account name

    TransactionPage:
      type: object
      required:
        - content
        - totalElements
        - totalPages
        - size
        - number
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        totalElements:
          type: integer
          format: int64
          description: Total number of transactions
        totalPages:
          type: integer
          description: Total number of pages
        size:
          type: integer
          description: Page size
        number:
          type: integer
          description: Current page number (0-based)
        first:
          type: boolean
          description: Whether this is the first page
        last:
          type: boolean
          description: Whether this is the last page

    TransactionStatus:
      type: string
      enum:
        - PENDING
        - COMPLETED
        - FAILED
        - CANCELLED
      description: Transaction status

    TransactionType:
      type: string
      enum:
        - DEPOSIT
        - WITHDRAWAL
        - TRANSFER
        - PAYMENT
      description: Transaction type

    ErrorResponse:
      type: object
      required:
        - message
        - timestamp
      properties:
        message:
          type: string
          description: Error message
        details:
          type: string
          description: Detailed error information
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        path:
          type: string
          description: Request path that caused the error
