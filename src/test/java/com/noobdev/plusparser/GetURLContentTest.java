package com.noobdev.plusparser;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class GetURLContentTest
{
    GetURLContent urlContent;

    @BeforeEach
    void prepareService()
    {
        urlContent = new GetURLContent();
        Assertions.assertNotNull(urlContent);
    }

    @AfterEach
    void cleanupService()
    {
        urlContent = null;
    }

    @Test
    void testWithFileURL() throws IOException
    {
        final String testString = "testStringContent";

        final File f = File.createTempFile("test", "URLContent");
        // mark to delete on exit
        f.deleteOnExit();

        try (FileWriter out = new FileWriter(f))
        {
            out.write(testString);
        }

        String fileContent = urlContent.getURLContent(f.toURI().toURL());

        Assertions.assertNotNull(fileContent);

        Assertions.assertEquals(testString, fileContent);
    }

    @Test
    void testWithMissingFileURL()
    {
        final File f = new File("testURLContentWrong");

        Assertions.assertFalse(f.exists());

        Assertions.assertThrows(RuntimeException.class, ()
         -> urlContent.getURLContent(f.toURI().toURL()));
    }
}
