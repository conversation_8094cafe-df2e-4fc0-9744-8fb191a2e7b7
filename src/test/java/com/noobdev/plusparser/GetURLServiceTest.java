package com.noobdev.plusparser;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class GetURLServiceTest
{
    GetURLService uriService;

    @BeforeEach
    void prepareService()
    {
        uriService = new GetURLService();
        Assertions.assertNotNull(uriService);
    }

    @AfterEach
    void cleanupService()
    {
        uriService = null;
    }

    @Test
    void testURL()
    {

        Assertions.assertNotNull(uriService.getURL("https://www.example.com"));
        Assertions.assertNotNull(uriService.getURL("http://www.example.com"));
    }

    @Test
    void testWrongURL()
    {
        Assertions.assertThrows(RuntimeException.class, () -> uriService.getURL("wrongUrl"));
        Assertions.assertThrows(RuntimeException.class, () -> uriService.getURL("www.example.com"));
    }
}
