package com.mymueller.transactionsstore;

import com.mymueller.transactionsstore.controller.ParticipantsController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Tests for the ParticipantsController
 */
@WebMvcTest(controllers = {ParticipantsController.class})
@ActiveProfiles("test")
class ParticipantsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testGetParticipants() throws Exception {
        mockMvc.perform(get("/api/v1/participants"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
    }

    @Test
    void testGetParticipantsReturnsEmptyList() throws Exception {
        mockMvc.perform(get("/api/v1/participants"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().json("[]"));
    }
}
