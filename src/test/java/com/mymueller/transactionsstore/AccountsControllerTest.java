package com.mymueller.transactionsstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mymueller.transactionsstore.controller.AccountsController;
import com.mymueller.transactionsstore.model.CreateAccountRequest;
import com.mymueller.transactionsstore.model.UpdateAccountRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Tests for the AccountsController
 */
@WebMvcTest(controllers = {AccountsController.class})
@ActiveProfiles("test")
class AccountsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AccountsController accountsController;

    @BeforeEach
    void setUp() {
        // Clear accounts before each test
        accountsController.clearAccounts();
    }

    @Test
    void testCreateAccount() throws Exception {
        CreateAccountRequest request = new CreateAccountRequest();
        request.setName("My Test Account");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.id").value("acc-1"))
                .andExpect(jsonPath("$.name").value("My Test Account"));
    }

    @Test
    void testCreateAccountWithMaxLengthName() throws Exception {
        // Create a name with exactly 150 characters
        String maxLengthName = "A".repeat(150);
        
        CreateAccountRequest request = new CreateAccountRequest();
        request.setName(maxLengthName);

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name").value(maxLengthName));
    }

    @Test
    void testCreateAccountWithEmptyName() throws Exception {
        CreateAccountRequest request = new CreateAccountRequest();
        request.setName("");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateAccountWithNullName() throws Exception {
        CreateAccountRequest request = new CreateAccountRequest();
        request.setName(null);

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateAccountWithTooLongName() throws Exception {
        // Create a name with 151 characters (exceeds max length)
        String tooLongName = "A".repeat(151);

        CreateAccountRequest request = new CreateAccountRequest();
        request.setName(tooLongName);

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testCreateAccountWithWhitespaceOnlyName() throws Exception {
        CreateAccountRequest request = new CreateAccountRequest();
        request.setName("   ");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetAccountById() throws Exception {
        // First create an account
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setName("Test Account");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Then retrieve it
        mockMvc.perform(get("/api/v1/accounts/acc-1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("acc-1"))
                .andExpect(jsonPath("$.name").value("Test Account"));
    }

    @Test
    void testGetAccountByIdNotFound() throws Exception {
        mockMvc.perform(get("/api/v1/accounts/non-existent"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testUpdateAccount() throws Exception {
        // First create an account
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setName("Original Name");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Then update it
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setName("Updated Name");

        mockMvc.perform(put("/api/v1/accounts/acc-1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("acc-1"))
                .andExpect(jsonPath("$.name").value("Updated Name"));
    }

    @Test
    void testUpdateAccountNotFound() throws Exception {
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setName("Updated Name");

        mockMvc.perform(put("/api/v1/accounts/non-existent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNotFound());
    }

    @Test
    void testUpdateAccountWithEmptyName() throws Exception {
        // First create an account
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setName("Original Name");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Try to update with empty name
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setName("");

        mockMvc.perform(put("/api/v1/accounts/acc-1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testUpdateAccountWithTooLongName() throws Exception {
        // First create an account
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setName("Original Name");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Try to update with too long name
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setName("A".repeat(151));

        mockMvc.perform(put("/api/v1/accounts/acc-1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testUpdateAccountWithMaxLengthName() throws Exception {
        // First create an account
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setName("Original Name");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated());

        // Update with max length name (150 characters)
        String maxLengthName = "B".repeat(150);
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setName(maxLengthName);

        mockMvc.perform(put("/api/v1/accounts/acc-1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value("acc-1"))
                .andExpect(jsonPath("$.name").value(maxLengthName));
    }

    @Test
    void testCreateMultipleAccounts() throws Exception {
        // Create first account
        CreateAccountRequest request1 = new CreateAccountRequest();
        request1.setName("Account 1");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value("acc-1"));

        // Create second account
        CreateAccountRequest request2 = new CreateAccountRequest();
        request2.setName("Account 2");

        mockMvc.perform(post("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value("acc-2"));

        // Verify both accounts exist
        mockMvc.perform(get("/api/v1/accounts/acc-1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Account 1"));

        mockMvc.perform(get("/api/v1/accounts/acc-2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Account 2"));
    }
}
