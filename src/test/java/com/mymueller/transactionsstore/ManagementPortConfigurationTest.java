package com.mymueller.transactionsstore;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test to verify management port configuration is correctly loaded
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "management.server.port=8081",
    "management.server.address=127.0.0.1"
})
class ManagementPortConfigurationTest {

    @Test
    void contextLoads() {
        // This test verifies that the Spring context loads successfully
        // with the management port configuration
        assertThat(true).isTrue();
    }
}
