package com.mymueller.transactionsstore;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Simple test repository to verify H2 database setup
 */
@Repository
public interface TestEntityRepository extends JpaRepository<TestEntity, Long> {
    
    List<TestEntity> findByName(String name);
    
    List<TestEntity> findByNameContaining(String nameFragment);
    
    @Query("SELECT t FROM TestEntity t WHERE t.description IS NOT NULL")
    List<TestEntity> findAllWithDescription();
}
