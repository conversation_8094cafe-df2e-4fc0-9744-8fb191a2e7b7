package com.mymueller.transactionsstore;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to verify JPA/Hibernate works correctly with H2 database
 */
@DataJpaTest
@ActiveProfiles("test")
class H2JpaIntegrationTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private TestEntityRepository repository;

    @BeforeEach
    void setUp() {
        // Clean up before each test
        repository.deleteAll();
        entityManager.flush();
        entityManager.clear();
    }

    @Test
    void testEntityManagerIsConfigured() {
        assertNotNull(entityManager, "EntityManager should be configured");
        assertNotNull(repository, "Repository should be configured");
    }

    @Test
    void testSaveAndFindEntity() {
        // Given
        TestEntity entity = new TestEntity("Test Name", "Test Description");

        // When
        TestEntity savedEntity = repository.save(entity);
        entityManager.flush(); // Force immediate persistence

        // Then
        assertNotNull(savedEntity.getId(), "Saved entity should have an ID");
        assertEquals("Test Name", savedEntity.getName());
        assertEquals("Test Description", savedEntity.getDescription());

        // Verify we can find it
        Optional<TestEntity> foundEntity = repository.findById(savedEntity.getId());
        assertTrue(foundEntity.isPresent(), "Entity should be found by ID");
        assertEquals("Test Name", foundEntity.get().getName());
    }

    @Test
    void testFindByName() {
        // Given
        TestEntity entity1 = new TestEntity("Unique Name", "Description 1");
        TestEntity entity2 = new TestEntity("Another Name", "Description 2");
        TestEntity entity3 = new TestEntity("Unique Name", "Description 3");

        repository.saveAll(List.of(entity1, entity2, entity3));
        entityManager.flush();

        // When
        List<TestEntity> foundEntities = repository.findByName("Unique Name");

        // Then
        assertEquals(2, foundEntities.size(), "Should find 2 entities with 'Unique Name'");
        assertTrue(foundEntities.stream().allMatch(e -> "Unique Name".equals(e.getName())));
    }

    @Test
    void testFindByNameContaining() {
        // Given
        TestEntity entity1 = new TestEntity("Test Entity One", "Description 1");
        TestEntity entity2 = new TestEntity("Another Test", "Description 2");
        TestEntity entity3 = new TestEntity("Different Name", "Description 3");

        repository.saveAll(List.of(entity1, entity2, entity3));
        entityManager.flush();

        // When
        List<TestEntity> foundEntities = repository.findByNameContaining("Test");

        // Then
        assertEquals(2, foundEntities.size(), "Should find 2 entities containing 'Test'");
        assertTrue(foundEntities.stream().allMatch(e -> e.getName().contains("Test")));
    }

    @Test
    void testCustomQuery() {
        // Given
        TestEntity entityWithDesc = new TestEntity("With Description", "Has description");
        TestEntity entityWithoutDesc = new TestEntity("Without Description", null);

        repository.saveAll(List.of(entityWithDesc, entityWithoutDesc));
        entityManager.flush();

        // When
        List<TestEntity> entitiesWithDescription = repository.findAllWithDescription();

        // Then
        assertEquals(1, entitiesWithDescription.size(), "Should find 1 entity with description");
        assertEquals("With Description", entitiesWithDescription.get(0).getName());
    }

    @Test
    void testTransactionRollback() {
        // Given
        TestEntity entity = new TestEntity("Test Entity", "Description");
        repository.save(entity);
        entityManager.flush();

        long initialCount = repository.count();
        assertTrue(initialCount > 0, "Should have at least one entity");

        // When we clear and check again (simulating transaction boundary)
        entityManager.clear();
        
        // Then - in @DataJpaTest, each test method runs in its own transaction that rolls back
        // But within the same test, data persists
        long countAfterClear = repository.count();
        assertEquals(initialCount, countAfterClear, "Count should remain the same within test");
    }

    @Test
    void testSchemaCreation() {
        // This test verifies that Hibernate can create the schema automatically
        // If H2 and Hibernate are configured correctly, this should work without issues
        
        TestEntity entity = new TestEntity("Schema Test", "Testing schema creation");
        TestEntity savedEntity = repository.save(entity);
        
        assertNotNull(savedEntity.getId(), "Entity should be saved with generated ID");
        assertTrue(savedEntity.getId() > 0, "Generated ID should be positive");
    }
}
