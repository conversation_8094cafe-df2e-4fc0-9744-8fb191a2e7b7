package com.mymueller.transactionsstore;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests to verify that management endpoints run on a separate port
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class ManagementPortTest {

    @LocalServerPort
    private int serverPort;

    @LocalManagementPort
    private int managementPort;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void testManagementPortIsDifferentFromServerPort() {
        // Verify that management port is different from server port
        assertThat(managementPort).isNotEqualTo(serverPort);
        assertThat(managementPort).isGreaterThan(0);
        assertThat(serverPort).isGreaterThan(0);
        
        System.out.println("Server port: " + serverPort);
        System.out.println("Management port: " + managementPort);
    }

    @Test
    void testHealthEndpointOnManagementPort() {
        // Health endpoint should be available on management port
        String healthUrl = "http://localhost:" + managementPort + "/actuator/health";
        
        ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("UP");
    }

    @Test
    void testInfoEndpointOnManagementPort() {
        // Info endpoint should be available on management port
        String infoUrl = "http://localhost:" + managementPort + "/actuator/info";
        
        ResponseEntity<String> response = restTemplate.getForEntity(infoUrl, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    void testMetricsEndpointOnManagementPort() {
        // Metrics endpoint should be available on management port
        String metricsUrl = "http://localhost:" + managementPort + "/actuator/metrics";
        
        ResponseEntity<String> response = restTemplate.getForEntity(metricsUrl, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).contains("names");
    }

    @Test
    void testHealthEndpointNotAvailableOnServerPort() {
        // Health endpoint should NOT be available on main server port
        String healthUrl = "http://localhost:" + serverPort + "/actuator/health";
        
        ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
        
        // Should return 404 since actuator endpoints are on management port
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }

    @Test
    void testApplicationEndpointsOnServerPort() {
        // Application endpoints should be available on server port
        String accountsUrl = "http://localhost:" + serverPort + "/api/v1/accounts";
        
        ResponseEntity<String> response = restTemplate.getForEntity(accountsUrl, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    void testApplicationEndpointsNotAvailableOnManagementPort() {
        // Application endpoints should NOT be available on management port
        String accountsUrl = "http://localhost:" + managementPort + "/api/v1/accounts";
        
        ResponseEntity<String> response = restTemplate.getForEntity(accountsUrl, String.class);
        
        // Should return 404 since application endpoints are on server port
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
    }
}
