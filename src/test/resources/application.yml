server:
  port: 0  # Use random port for tests

spring:
  application:
    name: transactionsstore-test
  
  # H2 Database configuration for tests
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA/Hibernate configuration for H2
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop  # Recreate schema for each test
    show-sql: true  # Enable SQL logging for debugging tests
    properties:
      hibernate:
        format_sql: true
  
  # H2 Console (useful for debugging tests)
  h2:
    console:
      enabled: true
      path: /h2-console

# Disable management endpoints for tests
management:
  endpoints:
    enabled-by-default: false

# Logging configuration for tests
logging:
  level:
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
