# Prometheus configuration for Transaction Store monitoring
# This configuration accesses management endpoints internally

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # Add alerting rules here if needed

scrape_configs:
  # Transaction Store application metrics
  - job_name: 'transactionstore'
    static_configs:
      # Access management port internally (not exposed externally)
      - targets: ['transactionstore:8081']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
    scrape_timeout: 10s
    
  # Application health monitoring
  - job_name: 'transactionstore-health'
    static_configs:
      - targets: ['transactionstore:8081']
    metrics_path: '/actuator/health'
    scrape_interval: 30s
    
  # JVM metrics
  - job_name: 'transactionstore-jvm'
    static_configs:
      - targets: ['transactionstore:8081']
    metrics_path: '/actuator/metrics'
    scrape_interval: 30s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

# Example alerting rules (optional)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093
