# GitLab CI/CD Pipeline Documentation

## Overview

This document describes the GitLab CI/CD pipeline configuration for the Transaction Store application. The pipeline is designed to build, test, and package the Spring Boot application with comprehensive coverage reporting and Docker preparation.

## Pipeline Stages

### 🏗️ Build Stage

**Purpose**: Compile the application and generate OpenAPI code

**Jobs**: `build`

**What it does**:
- Generates OpenAPI interfaces and models from `transaction-api.yaml`
- Compiles Java source code
- Creates build artifacts for subsequent stages

**Artifacts**:
- Generated OpenAPI code (`build/generated/`)
- Compiled classes (`build/classes/`)
- Resources (`build/resources/`)

**Duration**: ~2-3 minutes

### 🧪 Test Stage

**Purpose**: Execute tests and verify code coverage

**Jobs**: `test`

**What it does**:
- Runs all unit and integration tests (29 tests)
- Generates JaCoCo coverage reports (HTML + XML)
- Verifies coverage thresholds:
  - Instructions: ≥85%
  - Branches: ≥65%
  - Lines: ≥50% (per class)
- Displays coverage summary with emojis

**Artifacts**:
- Test reports (`build/reports/tests/test/`)
- Coverage reports (`build/reports/jacoco/test/`)
- JUnit XML results (`build/test-results/test/`)

**Coverage Integration**:
- GitLab displays coverage percentage in merge requests
- Coverage reports available in GitLab UI
- Pipeline fails if coverage drops below thresholds

**Duration**: ~3-5 minutes

### 📦 Package Stage

**Purpose**: Create deployable artifacts and prepare for containerization

**Jobs**: `package`

**What it does**:
- Builds executable Spring Boot JAR (`bootJar`)
- Creates optimized `Dockerfile` with multi-stage build
- Generates `.dockerignore` for efficient Docker builds
- Prepares artifacts for Docker image creation

**Artifacts**:
- Executable JAR file (`build/libs/*.jar`)
- `Dockerfile` (production-ready)
- `.dockerignore` (optimized)

**Docker Features**:
- Multi-stage build for smaller images
- Non-root user for security
- Health check configuration
- Proper port exposure (8080)

**Duration**: ~2-3 minutes

## Pipeline Configuration

### Caching Strategy

```yaml
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - build/
```

**Benefits**:
- Faster builds (dependencies cached)
- Reduced network usage
- Consistent build environment

### Artifact Management

**Build Artifacts** (1 hour retention):
- Generated code and compiled classes
- Used by test and package stages

**Test Artifacts** (1 week retention):
- Test reports and coverage data
- Available in GitLab UI
- Used for quality gates

**Package Artifacts** (1 week retention):
- Deployable JAR and Docker files
- Ready for deployment stages

### Branch Strategy

Pipeline runs on:
- ✅ `main` branch (production)
- ✅ `develop` branch (development)
- ✅ Merge requests (quality gates)
- ✅ Tags (releases)

## Usage Examples

### Running Locally

```bash
# Simulate build stage
./gradlew openApiGenerate compileJava

# Simulate test stage
./gradlew testWithCoverage

# Simulate package stage
./gradlew bootJar
```

### GitLab Integration

**Merge Request Workflow**:
1. Developer creates merge request
2. Pipeline runs automatically
3. Coverage and test results displayed
4. Merge blocked if tests fail or coverage drops

**Main Branch Workflow**:
1. Code merged to main
2. Full pipeline runs
3. Artifacts created and stored
4. Ready for deployment

## Extending the Pipeline

### Adding Docker Build

To add actual Docker image building, add this job after `package`:

```yaml
docker_build:
  stage: package
  image: docker:latest
  services:
    - docker:dind
  dependencies:
    - package
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
```

### Adding Deployment

```yaml
deploy_staging:
  stage: deploy
  script:
    - kubectl apply -f k8s/staging/
  environment:
    name: staging
    url: https://staging.transactionstore.com
  only:
    - develop
```

### Adding Security Scanning

Uncomment the `.security_scan` job and add dependency-check plugin to `build.gradle.kts`.

## Monitoring and Troubleshooting

### Common Issues

1. **Build Failures**: Check Java/Gradle versions
2. **Test Failures**: Review test logs in artifacts
3. **Coverage Drops**: Check coverage report details
4. **Cache Issues**: Clear cache in GitLab UI

### Performance Optimization

- **Parallel Jobs**: Tests can run in parallel with code quality
- **Selective Builds**: Use `rules:` instead of `only:` for complex conditions
- **Docker Layer Caching**: Enable in GitLab settings

### Monitoring

- Pipeline duration: Target <10 minutes total
- Coverage trends: Monitor in GitLab analytics
- Artifact sizes: Keep JAR <50MB

## Security Considerations

- ✅ Non-root Docker user
- ✅ Minimal base images
- ✅ No secrets in logs
- ✅ Dependency scanning ready
- ✅ Container scanning ready

## Next Steps

1. **Enable Docker Registry**: Configure GitLab Container Registry
2. **Add Deployment**: Create staging/production deployment jobs
3. **Security Scanning**: Enable dependency and container scanning
4. **Performance Testing**: Add load testing stage
5. **Notifications**: Configure Slack/email notifications
