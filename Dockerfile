# Use OpenJDK 17 as the base image
FROM eclipse-temurin:17-jdk-alpine as build

# Set the working directory
WORKDIR /workspace/app

# Copy the Gradle files
COPY gradlew .
COPY gradle gradle
COPY build.gradle.kts .
COPY settings.gradle.kts .

# Make the Gradle wrapper executable
RUN chmod +x gradlew

# Copy the source code
COPY src src

# Build the application with tests
RUN ./gradlew build

# Create a lightweight image for running the application
FROM eclipse-temurin:17-jre-alpine

# Set the working directory
WORKDIR /app

# Copy the built JAR file from the build stage
COPY --from=build /workspace/app/build/libs/*.jar app.jar

# Expose the port the application runs on
EXPOSE 8080

# Set the entry point
ENTRYPOINT ["java", "-jar", "app.jar", "--spring.profiles.active=prod"]
